import React, { useState, useEffect } from "react";
import { 
  Search, 
  Filter, 
  Building2, 
  CreditCard, 
  Edit3, 
  MoreVertical,
  Mail,
  Phone,
  MapPin,
  Calendar,
  TrendingUp,
  TrendingDown,
  Minus
} from "lucide-react";
import { AdminSearchBar, StatusBadge, FilterDropdown, ActionButton } from "./AdminComponents";
import avatar from "assets/img/avatars/avatar4.png";
import toast from "react-hot-toast";
import UnifiedManagementModal from "./UnifiedManagementModal";

const EmployerManagement = () => {
  const [employers, setEmployers] = useState([]);
  const [filteredEmployers, setFilteredEmployers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [planFilter, setPlanFilter] = useState("");
  const [selectedEmployer, setSelectedEmployer] = useState(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editCredits, setEditCredits] = useState("");

  // Mock data for demonstration
  const mockEmployers = [
    {
      id: 1,
      name: "John Smith",
      email: "<EMAIL>",
      designation: "HR Manager",
      company: "TechCorp Solutions",
      profile_picture: null,
      creditsLeft: 8,
      totalCredits: 10,
      planName: "Free Employer Plan",
      membershipStatus: "active",
      joinDate: "2024-01-15",
      lastActive: "2024-01-20",
      profilesUnlocked: 12,
      location: "Bangalore"
    },
    {
      id: 2,
      name: "Sarah Johnson",
      email: "<EMAIL>",
      designation: "Talent Acquisition Lead",
      company: "Innovate Labs",
      profile_picture: null,
      creditsLeft: 2,
      totalCredits: 10,
      planName: "Free Employer Plan",
      membershipStatus: "warning",
      joinDate: "2024-01-10",
      lastActive: "2024-01-19",
      profilesUnlocked: 28,
      location: "Mumbai"
    },
    {
      id: 3,
      name: "Michael Chen",
      email: "<EMAIL>",
      designation: "Co-Founder",
      company: "StartupX",
      profile_picture: null,
      creditsLeft: 0,
      totalCredits: 10,
      planName: "Free Employer Plan",
      membershipStatus: "expired",
      joinDate: "2023-12-20",
      lastActive: "2024-01-18",
      profilesUnlocked: 45,
      location: "Delhi"
    }
  ];

  useEffect(() => {
    const fetchEmployers = async () => {
      setLoading(true);
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        setEmployers(mockEmployers);
        setFilteredEmployers(mockEmployers);
      } catch (error) {
        console.error("Error fetching employers:", error);
        toast.error("Failed to load employers");
      } finally {
        setLoading(false);
      }
    };

    fetchEmployers();
  }, []);

  // Filter employers based on search and filters
  useEffect(() => {
    let filtered = employers;

    if (searchTerm) {
      filtered = filtered.filter(emp => 
        emp.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        emp.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        emp.company.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter) {
      filtered = filtered.filter(emp => emp.membershipStatus === statusFilter);
    }

    if (planFilter) {
      filtered = filtered.filter(emp => emp.planName === planFilter);
    }

    setFilteredEmployers(filtered);
  }, [searchTerm, statusFilter, planFilter, employers]);

  const handleEditCredits = (employer) => {
    setSelectedEmployer(employer);
    setEditCredits(employer.creditsLeft.toString());
    setShowEditModal(true);
  };

  const handleSaveCredits = async () => {
    if (!selectedEmployer) return;

    try {
      const newCredits = parseInt(editCredits);
      if (isNaN(newCredits) || newCredits < 0) {
        toast.error("Please enter a valid number of credits");
        return;
      }

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      // Update local state
      const updatedEmployers = employers.map(emp => 
        emp.id === selectedEmployer.id 
          ? { 
              ...emp, 
              creditsLeft: newCredits,
              membershipStatus: newCredits > 5 ? 'active' : newCredits > 0 ? 'warning' : 'expired'
            }
          : emp
      );
      
      setEmployers(updatedEmployers);
      setShowEditModal(false);
      setSelectedEmployer(null);
      setEditCredits("");
      
      toast.success(`Credits updated successfully for ${selectedEmployer.name}`);
    } catch (error) {
      console.error("Error updating credits:", error);
      toast.error("Failed to update credits");
    }
  };

  const EmployerCard = ({ employer }) => (
    <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-200">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-4">
          <div className="relative">
            <img
              src={employer.profile_picture || avatar}
              alt={employer.name}
              className="w-12 h-12 rounded-full object-cover border-2 border-gray-200 dark:border-gray-600"
            />
            <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white dark:border-gray-800 ${
              employer.membershipStatus === 'active' ? 'bg-green-500' :
              employer.membershipStatus === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
            }`}></div>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {employer.name}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {employer.designation} at {employer.company}
            </p>
            <div className="flex items-center gap-4 mt-1 text-xs text-gray-500 dark:text-gray-400">
              <span className="flex items-center gap-1">
                <Mail className="w-3 h-3" />
                {employer.email}
              </span>
              <span className="flex items-center gap-1">
                <MapPin className="w-3 h-3" />
                {employer.location}
              </span>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <StatusBadge status={employer.membershipStatus} type="membership" />
          <ActionButton
            onClick={() => handleEditCredits(employer)}
            variant="secondary"
            size="xs"
            icon={Edit3}
          >
            Edit
          </ActionButton>
        </div>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
        <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
            {employer.creditsLeft}
          </div>
          <div className="text-xs text-blue-600 dark:text-blue-400 font-medium">
            Credits Left
          </div>
        </div>
        <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <div className="text-2xl font-bold text-green-600 dark:text-green-400">
            {employer.profilesUnlocked}
          </div>
          <div className="text-xs text-green-600 dark:text-green-400 font-medium">
            Profiles Unlocked
          </div>
        </div>
        <div className="text-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
          <div className="text-sm font-semibold text-purple-600 dark:text-purple-400">
            {employer.planName}
          </div>
          <div className="text-xs text-purple-600 dark:text-purple-400 font-medium">
            Current Plan
          </div>
        </div>
        <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="text-sm font-semibold text-gray-600 dark:text-gray-400">
            {new Date(employer.lastActive).toLocaleDateString()}
          </div>
          <div className="text-xs text-gray-600 dark:text-gray-400 font-medium">
            Last Active
          </div>
        </div>
      </div>

      {/* Credit Usage Progress Bar */}
      <div className="mb-2">
        <div className="flex justify-between text-sm mb-1">
          <span className="text-gray-600 dark:text-gray-400">Credit Usage</span>
          <span className="text-gray-900 dark:text-white font-medium">
            {employer.totalCredits - employer.creditsLeft} / {employer.totalCredits}
          </span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${
              employer.creditsLeft > 5 ? 'bg-green-500' :
              employer.creditsLeft > 0 ? 'bg-yellow-500' : 'bg-red-500'
            }`}
            style={{
              width: `${((employer.totalCredits - employer.creditsLeft) / employer.totalCredits) * 100}%`
            }}
          ></div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="p-6">
      {/* Header and Search */}
      <div className="flex flex-col lg:flex-row gap-4 mb-6">
        <AdminSearchBar
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          placeholder="Search employers by name, email, or company..."
          loading={loading}
        />
        <div className="flex gap-2">
          <FilterDropdown
            label="Status"
            options={[
              { label: "Active", value: "active" },
              { label: "Warning", value: "warning" },
              { label: "Expired", value: "expired" }
            ]}
            value={statusFilter}
            onChange={setStatusFilter}
            icon={Filter}
          />
          <FilterDropdown
            label="Plan"
            options={[
              { label: "Free Employer Plan", value: "Free Employer Plan" },
              { label: "Pro Plan", value: "Pro Plan" },
              { label: "Enterprise Plan", value: "Enterprise Plan" }
            ]}
            value={planFilter}
            onChange={setPlanFilter}
            icon={CreditCard}
          />
        </div>
      </div>

      {/* Results Summary */}
      <div className="mb-6">
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Showing {filteredEmployers.length} of {employers.length} employers
        </p>
      </div>

      {/* Employers Grid */}
      {loading ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {[...Array(6)].map((_, index) => (
            <div key={index} className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 animate-pulse">
              <div className="flex items-center gap-4 mb-4">
                <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
                </div>
              </div>
              <div className="grid grid-cols-4 gap-4">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="h-16 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
                ))}
              </div>
            </div>
          ))}
        </div>
      ) : filteredEmployers.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredEmployers.map((employer) => (
            <EmployerCard key={employer.id} employer={employer} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <Building2 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No employers found
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Try adjusting your search criteria or filters
          </p>
        </div>
      )}

      {/* Unified Management Modal for Employer Credits */}
      <UnifiedManagementModal
        isOpen={showEditModal}
        onClose={() => {
          setShowEditModal(false);
          setSelectedEmployer(null);
          setEditCredits("");
        }}
        onSave={handleSaveCredits}
        selectedEntity={selectedEmployer}
        entityType="employer"
        value={editCredits}
        setValue={setEditCredits}
      />
    </div>
  );
};

export default EmployerManagement;
