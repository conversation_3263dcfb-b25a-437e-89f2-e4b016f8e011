import React, { useEffect, useState, useRef } from "react";
import { Search, Filter } from "lucide-react";
import AdvancedSearchModal from "../employer/components/AdvancedSearchModal";
import { HiBriefcase, HiHeart, HiLocationMarker, HiOutlineHeart, HiOutlineLocationMarker,
         HiOutlineUserGroup, HiOutlineVideoCamera, HiVideoCamera, HiChevronRight} from "react-icons/hi";
import Cookies from "js-cookie";
import ProfileSkelLoader from "../employer/components/ProfileSkelLoader";
import { useLocation, useNavigate } from "react-router-dom";
import toast from "react-hot-toast";
import { MdCallToAction } from "react-icons/md";
import Dropdown from "components/dropdown";
import Tooltip from "../../components/tooltip";
import UnifiedManagementModal from "./components/UnifiedManagementModal";


export const ProfileCard = ({keyValue, experience_range, shortListedProfiles,score,video_profile_id,candidateDetails,role,id,onShortlist,
  isShortlisted, isLoading,cand_id,skills, openTooltipId, setOpenTooltipId, onManageVisumes}) => {

    const [openTooltipType, setOpenTooltipType] = useState(null); // 'location' or 'skills'
    const locationRef = useRef(null);
    const skillsRef = useRef(null);

const skillsArray = skills ? skills.split(",").map((skill) => skill.trim()): [];

const [imageError, setImageError] = useState(false);
const emp_id = Cookies.get("employerId");
// console.log(emp_id)
// console.log(imageError)
const [hasViewed, setHasViewed] = useState(false);
const [hasClicked, setHasClicked] = useState(false);

const isLocationTooltipOpen = openTooltipId === video_profile_id && openTooltipType === 'location';
const isSkillsTooltipOpen = openTooltipId === video_profile_id && openTooltipType === 'skills';

const handleToggleTooltip = (e, type) => {
  e.stopPropagation();
  if (openTooltipId === video_profile_id && openTooltipType === type) {
    setOpenTooltipId(null);
    setOpenTooltipType(null);
  } else {
    setOpenTooltipId(video_profile_id);
    setOpenTooltipType(type);
  }
};

const profileClick = async () => {
const currentUrl = window.location.href;
localStorage.setItem("previousUrl", currentUrl);
window.open(`/profile/${video_profile_id}`);
if (!hasClicked) {
try {
if (!emp_id) {
return toast.error(
"You need to be an employer to shortlist profiles"
);
}

const response = await fetch(
`${import.meta.env.VITE_APP_HOST}/api/v1/candidate/analytics`,
{
method: "POST",
headers: {
"Content-Type": "application/json",
},
body: JSON.stringify({
employer_id: emp_id,
profile_id: id,
interaction_type: "click",
}),
}
);

if (!response.ok) {
const msg = await response.json();
toast.error(msg.message);
throw new Error(`HTTP error! status: ${msg.message}`);
}

const data = await response.json();
console.log(data);
} catch (err) {
console.log(err);
} finally {
setHasClicked(true);
}
}
};

const profileView = async () => {
if (!hasViewed) {
try {
if (!emp_id) {
return toast.error(
"You need to be an employer to shortlist profiles"
);
}

const response = await fetch(
`${import.meta.env.VITE_APP_HOST}/api/v1/candidate/analytics`,
{
method: "POST",
headers: {
"Content-Type": "application/json",
},
body: JSON.stringify({
employer_id: emp_id,
profile_id: id,
interaction_type: "view",
}),
}
);

if (!response.ok) {
const msg = await response.json();
toast.error(msg.message);
throw new Error(`HTTP error! status: ${msg.message}`);
}

const data = await response.json();
console.log(data);
} catch (err) {
console.log(err);
} finally {
setHasViewed(true);
}
}
};

return (

<div onMouseEnter={profileView} key={keyValue}
className="flex flex-col justify-between rounded-2xl bg-gradient-to-br from-white via-gray-50 to-gray-100 border border-gray-200 shadow-xl p-5 transition-all duration-200 hover:shadow-2xl w-auto min-w-fit max-w-[350px]"
>
{/* Header Row: Profile Image and Name */}
<div className="flex items-center gap-4 flex-nowrap">
{/* Profile Image */}
<div className="h-16 w-16 flex-shrink-0 cursor-pointer rounded-full border-4 border-white shadow-md bg-gray-200 overflow-hidden" onClick={profileClick}>

{imageError ? (
// Render this div if image load fails
<div className="flex h-full w-full items-center justify-center rounded-full bg-brand-600 text-2xl font-semibold text-white">
{candidateDetails[0].cand_name[0].toUpperCase()}
</div>
) : (
// Render the image by default
<img
className="h-full w-full rounded-full object-cover"
src={`${import.meta.env.VITE_APP_HOST}/${
candidateDetails[0].profile_picture
}`}
alt={candidateDetails[0].cand_name}
onError={() => setImageError(true)} // Set error to true if image fails to load
/>
)}
</div>
{/* Name and Icon */}
<div className="flex items-center min-w-0 flex-shrink gap-1 max-w-[180px] sm:max-w-[220px]">
<h2
className="text-lg font-bold text-gray-900 min-w-0 overflow-hidden text-ellipsis whitespace-nowrap"
title={candidateDetails[0].cand_name.length > 25 ? candidateDetails[0].cand_name : undefined}
>
{candidateDetails[0].cand_name.length > 25
? candidateDetails[0].cand_name.slice(0, 25) + '...'
: candidateDetails[0].cand_name}
</h2>
<span className="ml-1 text-indigo-500 flex-shrink-0">
<HiBriefcase />
</span>
</div>
</div>
{/* Info Section: Score, Role, Experience, Salary */}
<div className="flex flex-col gap-2 mt-2">
{/* Score Row */}
<div className="w-fit">
<span className="inline-flex items-center gap-1 h-7 px-2 py-0.5 rounded-full bg-gradient-to-br from-purple-200 to-indigo-200 text-base font-bold text-indigo-700 shadow">
<span className="text-sm font-medium">Score:</span>
<span>{Math.round(JSON.parse(score)?.score?.Overall_Score) || 0}</span>
</span>
</div>
{/* Role, Experience, Salary */}
<div className="flex flex-wrap gap-2 text-xs text-gray-500">
<span className="inline-flex items-center gap-1 rounded-full bg-purple-100 px-3 py-0.5 text-xs font-semibold text-purple-800 border border-purple-200">
{role}
</span>
<span className="inline-flex items-center gap-1 rounded-full bg-blue-50 px-3 py-0.5 text-xs font-semibold text-blue-700 border border-blue-200">
<span className="text-blue-400">
<HiBriefcase />
</span>
Exp: {experience_range}
</span>
<span className="inline-flex items-center gap-1 rounded-full bg-green-50 px-3 py-0.5 text-xs font-semibold text-green-700 border border-green-200">
₹12-14 LPA
</span>
</div>
</div>
{/* Skills and Location Tags (side by side, even spacing) */}
<div className="mt-2 flex flex-row flex-wrap gap-2 w-full text-xs text-gray-500 overflow-x-auto">
{/* Skills Tag */}
<div className="relative min-w-0">
<button
ref={skillsRef}
onClick={e => handleToggleTooltip(e, 'skills')}
className="flex items-center gap-1 rounded-full bg-indigo-50 px-2 py-1 text-xs font-semibold text-indigo-700 border border-indigo-200 hover:bg-indigo-100 transition-colors duration-200 cursor-pointer min-w-0"
type="button"
>
<svg className="w-4 h-4 text-indigo-400" fill="currentColor" viewBox="0 0 20 20">
<circle cx="10" cy="10" r="10" />
</svg>
<span className="truncate">Skills</span>
<HiChevronRight className={`ml-1 text-indigo-400 w-4 h-4 flex-shrink-0 transform transition-transform ${isSkillsTooltipOpen ? 'rotate-90' : 'rotate-0'}`} />
</button>
<Tooltip
content={skillsArray}
isOpen={isSkillsTooltipOpen}
onClose={() => { setOpenTooltipId(null); setOpenTooltipType(null); }}
targetRef={skillsRef}
title="Skills"
/>
</div>
{/* Location Tag */}
<div className="relative min-w-0">
<button
ref={locationRef}
onClick={e => handleToggleTooltip(e, 'location')}
className="flex items-center gap-1 rounded-full bg-indigo-50 px-2 py-1 text-xs font-semibold text-indigo-700 border border-indigo-200 hover:bg-indigo-100 transition-colors duration-200 cursor-pointer min-w-0"
type="button"
>
<HiOutlineLocationMarker className="text-indigo-400" />
<span className="truncate">Location</span>
<HiChevronRight className={`ml-1 text-indigo-400 w-4 h-4 flex-shrink-0 transform transition-transform ${isLocationTooltipOpen ? 'rotate-90' : 'rotate-0'}`} />
</button>
<Tooltip
content={(
candidateDetails[0].preferred_location.startsWith("[")
? JSON.parse(candidateDetails[0].preferred_location)
: [candidateDetails[0].preferred_location]
)}
isOpen={isLocationTooltipOpen}
onClose={() => { setOpenTooltipId(null); setOpenTooltipType(null); }}
targetRef={locationRef}
title="Preferred Locations"
/>
</div>
</div>
{/* Match Details */}
<div className="mt-4 flex items-center gap-2 text-xs">
{JSON.parse(score)?.score?.Experience_Match > 70 && (
<span className="inline-flex items-center gap-1 rounded-full bg-yellow-100 px-2 py-0.5 font-medium text-yellow-700">
Experience Match
</span>
)}
</div>

{/* Action Buttons */}
<div className="mt-4 flex gap-3 border-t border-gray-200 pt-4">
<button
onClick={profileClick}
className="flex flex-1 items-center justify-center gap-2 rounded-lg border border-indigo-200 bg-white text-indigo-700 hover:bg-indigo-50 hover:text-indigo-900 px-3 py-2 text-sm font-semibold shadow-sm transition-colors"
>
<HiVideoCamera className="text-lg" />
View Profile
</button>
<button
className="flex flex-1 items-center justify-center gap-2 rounded-lg border border-purple-200 bg-purple-50 text-purple-700 hover:bg-purple-100 hover:text-purple-900 px-3 py-2 text-sm font-semibold shadow-sm transition-colors"
onClick={() => onManageVisumes && onManageVisumes(candidateDetails[0], cand_id)}
>
<MdCallToAction className="text-base" />
Manage
</button>
 

  

</div>


</div>
);
};

const SearchBar = ({setProfiles, loading, setLoading, setSetShowLoadMore, getAllProfiles, setOldProfiles, oldProfiles, setViewInteraction,}) =>{


const [location, setLocation] = useState("");
const [role, setRole] = useState("");
const [skills, setSkills] = useState("");
const [focusedInput, setFocusedInput] = useState(null);
const [selectedSkills, setSelectedSkills] = useState([]);
const [showAdvancedModal, setShowAdvancedModal] = useState(false);
const [advancedFilters, setAdvancedFilters] = useState({
experience: "",
expectedSalary: "",
currentSalary: "",
score: "",
});
const [countAdvanceFilters, setCountAdvanceFilters] = useState(0);

const [roleSuggestion, setRoleSuggestion] = useState([]);
const [skillSuggestion, setSkillSuggestion] = useState([]);
const [locationSuggestion, setLocationSuggestion] = useState([]);

const jobRoles = [
"Software Developer/Engineer",
"Java Developer",
"Frontend Developer",
"Backend Developer",
"Full Stack Developer",
"DevOps Engineer",
];

const skillsList = [
"JavaScript",
"Python",
"React",
"Node.js",
"CSS",
"HTML",
"Tailwind CSS",
"Django",
"Java",
"Spring MVC",
];

const locations = ["Bangalore", "Delhi", "Mumbai", "Hyderabad"];

const query = new URLSearchParams(useLocation().search);
console.log(query)
//const navigate = useNavigate();

const handleAddSkill = (skill) => {
if (skill && !selectedSkills.includes(skill)) {
setSelectedSkills([...selectedSkills, skill]);
setSkills("");
}
};

const handleRemoveSkill = (skill) => {
setSelectedSkills(selectedSkills.filter((s) => s !== skill));
};

const handleSearch = async () => {
try {
if (
!location.trim() &&
!role.trim() &&
!selectedSkills.length &&
!Object.values(advancedFilters).some((value) => value)
) {
// navigate(`candidateProfiles`);
setProfiles([]);
getAllProfiles();
return;
}

setLoading(true);
const queryParams = new URLSearchParams({
...(location && { preferred_location: location }),
...(role && { role }),
...(selectedSkills.length && {
selectedSkills: JSON.stringify(selectedSkills),
}),
shortlisted: "false",
...(advancedFilters.experience && {
experience: advancedFilters.experience,
}),
...(advancedFilters.expectedSalary && {
expected_salary: advancedFilters.expectedSalary,
}),
...(advancedFilters.currentSalary && {
current_salary: advancedFilters.currentSalary,
}),
...(advancedFilters.score && { score: advancedFilters.score }),
});

const data = await fetch(
`${
import.meta.env.VITE_APP_HOST
}/api/v1/filterCandidate?${queryParams.toString()}`
);

const res = await data.json();
setProfiles(res.candidateProfiles);
setOldProfiles((prev) => {
const updatedProfiles = [...prev, ...res.candidateProfiles];
return updatedProfiles.slice(-10); // Keep only the last 10 profiles
});
// navigate(`candidateProfiles?${queryParams.toString()}`);
setViewInteraction((prev) => prev + 1);
setSetShowLoadMore(false);
} catch (err) {
console.error("Error fetching candidate profiles:", err);
} finally {
setLoading(false);
}
};

useEffect(() => {
handleSearch();
}, [advancedFilters, selectedSkills]);

useEffect(() => {
let count = 0;
for (const key in advancedFilters) {
if (advancedFilters[key].trim()) {
count += 1;
}
}
setCountAdvanceFilters(count);
}, [advancedFilters]);

useEffect(() => {
const preferredLocation = query.get("preferred_location") || "";
const roleParam = query.get("role") || "";
const experience = query.get("experience") || "";
const expectedSalary = query.get("expected_salary") || "";
const currentSalary = query.get("current_salary") || "";
const score = query.get("score") || "";
const skills = query.get("selectedSkills") || "[]";
const decodedSkills = JSON.parse(decodeURIComponent(skills));

// Update states with extracted values
setLocation(preferredLocation);
setRole(roleParam);
setAdvancedFilters({
experience,
expectedSalary,
currentSalary,
score,
});
setSelectedSkills(decodedSkills);
}, []);

useEffect(() => {
const advFilter = Object.values(advancedFilters).some((value) => value);
if (
!selectedSkills.length &&
!role &&
!location &&
!advFilter &&
oldProfiles.length
) {
//  navigate(`candidateProfiles`);
setProfiles((prev) => {
const updatedProfiles = [...prev, ...oldProfiles].slice(-10);
const uniqueProfiles = [
...new Map(updatedProfiles.map((item) => [item.id, item])).values(),
];
return uniqueProfiles.reverse();
});
}
}, [selectedSkills, role, location, advancedFilters, oldProfiles]);

return (
<div className="relative mx-auto mb-5 w-full pt-5 max-w-4xl px-3">
<div className="flex w-full flex-col gap-2 rounded-lg  bg-white p-1 shadow-md sm:p-3">
<div className="flex flex-col gap-2 sm:flex-row sm:items-start">
{/* Inputs Container */}
<div className="flex-1 space-y-2 sm:grid sm:grid-cols-3 sm:gap-2 sm:space-y-0">

<div className="relative flex items-center rounded-md px-3 focus-within:ring-2 focus-within:ring-indigo-500">

<HiLocationMarker  size={18} className={focusedInput === "location"? "text-indigo-500":"text-gray-400" }/>

<input type="text" placeholder="Location" value={location} onChange={(e) => {setLocation(e.target.value);

if (e.target.value) {
const locationSuggest = locations.filter((val) => val.toLowerCase().includes(e.target.value.toLowerCase()));
setLocationSuggestion(locationSuggest);
} else {
setLocationSuggestion([]);
}
}}

className="bg-transparent w-full py-2 pl-2 text-sm sm:text-base focus:outline-none "
onFocus={() => setFocusedInput("location")} onBlur={() => setFocusedInput(null)}/>

</div>

{locationSuggestion && locationSuggestion.length ? (
<div className="absolute top-20 z-50 flex max-h-[300px] w-[250px] flex-col overflow-y-auto rounded-lg border
  border-gray-300 bg-white shadow-lg">

{locationSuggestion.slice(0, 10).map((e, index) => (

<span onClick={() => {setLocation(e); setLocationSuggestion([]);}} key={index}
className="hover:text-black cursor-pointer rounded-t-md px-4 py-2 text-gray-800 transition-all duration-200
  hover:bg-gray-100">
{e}
</span>

))}
</div>
) : undefined}

<div className="relative flex items-center rounded-md px-3 focus-within:ring-2 focus-within:ring-indigo-500">

<HiBriefcase  size={18} className={ focusedInput === "role" ? "text-indigo-500" : "text-gray-400"}/>

<input type="text" placeholder="Role" value={role} onChange={(e) => {setRole(e.target.value);

if (e.target.value) {
const roleSuggest = jobRoles.filter((val) =>
val.toLowerCase().includes(e.target.value.toLowerCase())
);
setRoleSuggestion(roleSuggest);
} else {
setRoleSuggestion([]);
}
}}
className="bg-transparent w-full py-2 pl-2 text-sm focus:outline-none sm:text-base"
onFocus={() => setFocusedInput("role")} onBlur={() => setFocusedInput(null)}/>
</div>

{roleSuggestion && roleSuggestion.length ? (
<div className="absolute left-[25%] top-20 z-50 flex max-h-[300px] w-[250px] flex-col overflow-y-auto rounded-lg border
border-gray-300 bg-white shadow-lg">

{roleSuggestion.slice(0, 10).map((e, index) => (
<span onClick={() => {setRole(e); setRoleSuggestion([]); }} key={index}
className="hover:text-black cursor-pointer rounded-t-md px-4 py-2 text-gray-800 transition-all duration-200
   hover:bg-gray-100">
{e}
</span>
))}

</div>
) : undefined}

<div className="relative flex items-center rounded-md px-3 focus-within:ring-2 focus-within:ring-indigo-500">

<Filter size={18} className={focusedInput === "skills" ? "text-indigo-500" : "text-gray-400" }/>

<input type="text" placeholder="Skills" value={skills} onChange={(e) => {setSkills(e.target.value);

if (e.target.value) {
const skillSuggest = skillsList.filter((val) =>
val.toLowerCase().includes(e.target.value.toLowerCase())
);
setSkillSuggestion(skillSuggest);
} else {
setSkillSuggestion([]);
}
}}

className="bg-transparent w-full py-2 pl-2 text-sm focus:outline-none sm:text-base"
onFocus={() => setFocusedInput("skills")}
onBlur={() => setFocusedInput(null)} onKeyDown={(e) => {
if (e.key === "Enter") {
handleAddSkill(skills);
}
}}
/>

</div>

{skillSuggestion && skillSuggestion.length ? (
<div className="absolute left-[50%] top-20 z-50 flex max-h-[300px] w-[250px] flex-col overflow-y-auto rounded-lg border border-gray-300 bg-white shadow-lg">
{skillSuggestion.slice(0, 10).map((e, index) => (
<span
onClick={() => {
setSkills(e);
setSkillSuggestion([]);
}}
key={index}
className="hover:text-black cursor-pointer rounded-t-md px-4 py-2 text-gray-800 transition-all duration-200 hover:bg-gray-100"
>
{e}
</span>
))}
</div>
) : undefined}
</div>

{/* Buttons Container */}
<div className="flex h-10 gap-2 sm:ml-2">
<button
onClick={() => setShowAdvancedModal(true)}
className="relative flex flex-1 items-center justify-center gap-1 rounded-md border border-indigo-500 px-3 py-1.5 text-xs font-medium text-indigo-500 transition-colors hover:bg-indigo-50 sm:flex-initial"
>
<Filter size={16} />
Filters
{countAdvanceFilters > 0 && ( // Show the count only if it's greater than 0
<span className="absolute right-1 top-0 flex h-5 w-5 -translate-y-1/2 translate-x-1/2 transform items-center 
      justify-center rounded-full bg-red-500 text-white">
{countAdvanceFilters}
</span>
)}
</button>

<button
onClick={loading ? undefined : handleSearch}
className="flex flex-1 items-center justify-center gap-1 rounded-md bg-indigo-500 px-3 py-1.5 text-xs font-medium
text-white transition-colors hover:bg-indigo-600 sm:flex-initial" >
{loading ? (
<svg
className="h-4 w-4 animate-spin text-white"
viewBox="0 0 24 24"
>
<circle
className="opacity-25"
cx="12"
cy="12"
r="10"
stroke="currentColor"
strokeWidth="4"
fill="none"
/>
<path
className="opacity-75"
fill="currentColor"
d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
/>
</svg>
) : (
<>
<Search size={16} />
Search
</>
)}
</button>
</div>
</div>

{/* Selected Skills */}
{selectedSkills.length > 0 && (
<div className="flex flex-wrap gap-2">
{selectedSkills.map((skill) => (
<span
key={skill}
className="flex items-center rounded-full bg-indigo-100 px-3 py-1 text-xs text-indigo-800"
>
{skill}
<button
className="ml-2 text-indigo-600 hover:text-indigo-800"
onClick={() => handleRemoveSkill(skill)}
>
×
</button>
</span>
))}
</div>
)}
</div>
<div className="mt-2 flex items-center justify-start gap-2">
{Object.entries(advancedFilters)
.filter(([key, value]) => value) // Filter out empty values
.map(([key, value], index) => (
<div
key={index}
className="relative flex items-center space-x-2 rounded-full bg-brand-200 px-3 py-1 text-white"
>
<span className="px-2">{`${key}: ${value}`}</span>
<button
type="button"
className="absolute right-0 top-0 flex h-3 w-3 items-center justify-center rounded-full bg-red-500 text-sm text-white hover:bg-red-700"
onClick={() => {
const newFilters = { ...advancedFilters };
newFilters[key] = ""; // Clear the value of this filter
setAdvancedFilters(newFilters);
}}
>
&times;
</button>
</div>
))}
</div>
<AdvancedSearchModal
isOpen={showAdvancedModal}
onClose={() => {
setShowAdvancedModal(false);
}}
advancedFilters={advancedFilters}
setAdvancedFilters={setAdvancedFilters}
onApplyFilters={(filters) => {
setAdvancedFilters(filters);
}}
/>
</div>
);
};

const ProfileSearchUI = () => {

const navigate = useNavigate();
const [pageSize] = useState(10);
const [showLoadMore, setSetShowLoadMore] = useState(false);
const [loading, setLoading] = useState(false);
const [totalProfiles, setTotalProfiles] = useState(500);
const [selectedProfiles, setSelectedProfiles] = useState(new Set());
const [profiles, setProfiles] = useState([]);
const [loadingId, setLoadingId] = useState(null);
const [shortListedProfiles, setShortListedProfiles] = useState([]);
const [oldProfiles, setOldProfiles] = useState([]);
const [viewInteraction, setViewInteraction] = useState(0);
const [openTooltipId, setOpenTooltipId] = useState(null); // New state for managing open tooltip

// Visume Management Modal State
const [showVisumeModal, setShowVisumeModal] = useState(false);
const [selectedCandidate, setSelectedCandidate] = useState(null);
const [visumeLimit, setVisumeLimit] = useState("");
const [isUpdatingVisumes, setIsUpdatingVisumes] = useState(false);

// Handle opening visume management modal
const handleManageVisumes = (candidate, candId) => {
  setSelectedCandidate({ ...candidate, cand_id: candId });
  // Set default limit based on current usage or 1
  setVisumeLimit("1");
  setShowVisumeModal(true);
};

// Handle saving visume limit changes
const handleSaveVisumeLimit = async () => {
  if (!selectedCandidate) return;

  try {
    const newLimit = parseInt(visumeLimit);
    if (isNaN(newLimit) || newLimit < 0) {
      toast.error("Please enter a valid visume limit");
      return;
    }

    // 🎯 VISUME VALIDATION: Enforce minimum visume limit of 1
    if (newLimit < 1) {
      toast.error("Visume limit must be at least 1 Visume");
      return;
    }

    setIsUpdatingVisumes(true);

    const response = await fetch(
      `${import.meta.env.VITE_APP_HOST}/api/v1/admin/candidate/${selectedCandidate.cand_id}/limit`,
      {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ visume_limit: newLimit }),
      }
    );

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || 'Failed to update visume limit');
    }

    // Close modal and reset state
    setShowVisumeModal(false);
    setSelectedCandidate(null);
    setVisumeLimit("");

    toast.success(`Visume limit updated successfully for ${selectedCandidate.cand_name}`);

    // Optionally refresh the profiles list
    // getAllProfiles();

  } catch (error) {
    console.error("Error updating visume limit:", error);
    toast.error(error.message || "Failed to update visume limit");
  } finally {
    setIsUpdatingVisumes(false);
  }
};

const getAllProfiles = async (pageLength) => {
if (profiles.length < totalProfiles) {
try {
const dummyProfiles = Array.from({ length: 10 }, (_, index) => ({
name: `Loading... ${index}`,
}));

setProfiles((prev) => [...prev, ...dummyProfiles]);

const page = Math.ceil(profiles.length / 10);
const data = await fetch(
`${import.meta.env.VITE_APP_HOST}/api/v1/getAllCandidates?page=${
pageLength ? pageLength : page + 1
}&pageSize=${pageSize}`
);
const res = await data.json();
console.log(res)

setTotalProfiles(res.pagination.totalCandidates);
setProfiles((prev) => {
const newProfiles = prev.slice(0, prev.length - 10);
return [...newProfiles, ...res.candidateProfiles];
});

// Set viewInteraction to 1 to show the profiles
setViewInteraction(1);
setSetShowLoadMore(true);
} catch (err) {
console.log(`Error`, err);
}
}
};

useEffect(() => {
getAllProfiles()

}, []);

return (

<div className="min-h-screen bg-gradient-to-br from-gray-100 via-white to-gray-50 pb-8 ">

<div className={`container mx-auto  ${ !viewInteraction && "flex h-[60vh] flex-col items-center justify-center gap-8"}`}>

{/* {!viewInteraction && (
<div className="text-center mt-40 p-4 md:p-0 ">

<h1 className="text-2xl md:text-4xl font-extrabold tracking-tight text-gray-900 flex items-center gap-2 justify-center">
Search Video Resumes <HiOutlineVideoCamera/>
</h1>

<p className="mt-4 text-sm md:text-lg text-gray-600 max-w-2xl mx-auto">
Discover talented candidates by exploring their AI-powered video resumes.<br></br>Use advanced search tools to refine results.

</p>
</div>
)} */}

<SearchBar setLoading={setLoading} setOldProfiles={setOldProfiles} oldProfiles={oldProfiles} loading={loading} setProfiles={setProfiles}
setSetShowLoadMore={setSetShowLoadMore} getAllProfiles={getAllProfiles} setViewInteraction={setViewInteraction}/>


{viewInteraction ? (
<>
{/* Profile Grid */}
<div className="px-6 ">

<div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"> 

{profiles && profiles.length > 0 ? (profiles.map((profile) => profile.id ? (
<ProfileCard
shortListedProfiles={shortListedProfiles}
key={profile.id}
keyValue={profile.id}
{...profile}
openTooltipId={openTooltipId}
setOpenTooltipId={setOpenTooltipId}
onManageVisumes={handleManageVisumes}
// isShortlisted={selectedProfiles.has(profile.id)}
// onShortlist={handleShortlist} isLoading={loadingId === profile.id}
className="transform overflow-hidden rounded-xl bg-white shadow-lg transition duration-300 hover:scale-105 hover:shadow-2xl"
/>
) : (
<ProfileSkelLoader key={`skeleton-${Math.random()}`} />
)
)
) : (
<div className="col-span-full flex flex-col items-center justify-center py-20 text-gray-500">
<HiOutlineUserGroup className="text-5xl mb-6 text-gray-400 animate-pulse" />
<p className="text-center text-xl font-semibold">
No profiles found
</p>
<p className="text-center text-sm text-gray-500 mt-1">
Try different search parameters to find candidates.
</p>
</div>
)}
</div>

{/* Load More Button */}
{profiles && profiles.length < totalProfiles && showLoadMore && (
<div className="mt-12 flex justify-center">
<button
onClick={() => getAllProfiles()}
className="relative inline-flex items-center justify-center overflow-hidden rounded-full bg-gradient-to-r from-indigo-600 to-purple-600 px-8 py-4 text-sm font-semibold text-white shadow-lg transition-transform duration-300 hover:scale-105"
>
<span className="absolute inset-0 bg-gradient-to-r from-indigo-700 to-purple-700 blur-md"></span>
<span className="relative z-10 flex items-center space-x-2">
<HiOutlineUserGroup className="text-lg" />
<span>Load More Profiles</span>
</span>
</button>
</div>
)}
</div>
</>
) : null}

</div>

{/* Unified Management Modal for Visume Limits */}
<UnifiedManagementModal
  isOpen={showVisumeModal}
  onClose={() => {
    setShowVisumeModal(false);
    setSelectedCandidate(null);
    setVisumeLimit("");
  }}
  onSave={handleSaveVisumeLimit}
  selectedEntity={selectedCandidate}
  entityType="candidate"
  value={visumeLimit}
  setValue={setVisumeLimit}
  isUpdating={isUpdatingVisumes}
/>

</div>

);
};

export default ProfileSearchUI;
