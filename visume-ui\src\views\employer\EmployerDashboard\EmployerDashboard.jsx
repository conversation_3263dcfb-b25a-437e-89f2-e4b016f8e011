import Cookies from "js-cookie";
import JobDescriptionList from "./JobDescriptionList";
import React, { useEffect, useState, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { HiOutlineSparkles, HiOutlineBriefcase, HiOutlineTrash, HiOutlinePlus } from "react-icons/hi";
import { Bot, MessageCircle, CreditCard,ArrowUpCircle } from "lucide-react";
import {
  Header,
  StatsOverview,
  JobList,
  ProfileSkelLoader,
  JobDescriptionModal,
} from "./index";
import StatCard from "../components/StatCard";
import PositionsCard from "../components/PositionsCard";
import ProfileCard from "../ProfilesUI/ProfileCard";
import toast from "react-hot-toast";
import avatar from "assets/img/avatars/avatar4.png";
import { getFormattedEmployerMembershipStatus } from "services/employerMembershipService";
import UniversalMembershipModal from "components/shared/UniversalMembershipModal";


const EmployerDashboard = () => {
  const jobDescListRef = useRef();
  const emp_id = Cookies.get("employerId");

  const handleJobDescriptionChange = () => {
    if (jobDescListRef.current && jobDescListRef.current.refresh) {
      jobDescListRef.current.refresh();
    }
  };
  const jstoken = Cookies.get("jstoken");
  
  const navigate = useNavigate();
  const [shortListedCandidatesCount, setShortListedCandidatesCount] =
    useState(0);
  const [unlockedCandidatesCount, setUnlockedCandidatesCount] = useState(0);
  const [InterviewedCandidatesCount, setInterviewedCandidatesCount] =
    useState(0);
  const [offeredCandidatesCount, setOfferedCandidatesCount] = useState(0);
  const [empData, setEmpData] = useState({
    name: "Default User1",
    plan_name: "PRO",
    creditsLeft: 100,
  });

  const [isModalOpen, setModalOpen] = useState(false);
  const [jobData, setJobData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  // Job Description state
  const [dashboardJobDescription, setDashboardJobDescription] = useState(null);
  const [jdLoading, setJdLoading] = useState(false);
  // 🎯 EMPLOYER MEMBERSHIP: Modal state for credit limit handling
  const [showMembershipModal, setShowMembershipModal] = useState(false);
  const [membershipStatus, setMembershipStatus] = useState({});
  const handleShortlist = async (id) => {
    if (loadingId === id) return;
    setLoadingId(id);

    try {
      const emp_id = Cookies.get("employerId");
      if (!emp_id) {
        toast.error("You need to be an employer to shortlist profiles");
        return;
      }

      const response = await fetch(
        `${
          import.meta.env.VITE_APP_HOST
        }/api/v1/employer-profiles/shortlist-candidate`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ emp_id, cand_id: id }),
        }
      );

      if (!response.ok) {
        const msg = await response.json();
        toast.error(msg.message);
        throw new Error(`HTTP error! status: ${msg.message}`);
      }

      const data = await response.json();
      toast.success(data.message);
      // Remove the shortlisted profile from jobData
      setJobData((prev) =>
        prev.filter((profile) => profile.video_profile_id !== id)
      );
    } catch (error) {
      console.error("Error during shortlist operation:", error);
    } finally {
      setLoadingId(null);
    }
  };
  const [loadingId, setLoadingId] = useState(null);

  useEffect(() => {
    let isMounted = true;

    const filterMatchingProfiles = (profiles, jobDescription) => {
      if (!profiles || !jobDescription) return [];

      return profiles
        .filter((profile) => {
          // Check role match (case-insensitive)
          const roleMatches =
            profile.role.toLowerCase() === jobDescription.role.toLowerCase();

          // Check skills match
          const profileSkills = profile.skills
            .toLowerCase()
            .split(",")
            .map((s) => s.trim());
          const jdSkills = Array.isArray(jobDescription.skills)
            ? jobDescription.skills.map((s) => s.toLowerCase())
            : jobDescription.skills
                .toLowerCase()
                .split(",")
                .map((s) => s.trim());

          const skillsMatch = jdSkills.some((skill) =>
            profileSkills.includes(skill)
          );

          return roleMatches && skillsMatch;
        })
        .sort((a, b) => {
          // Sort by number of matching skills
          const aSkills = a.skills
            .toLowerCase()
            .split(",")
            .map((s) => s.trim());
          const bSkills = b.skills
            .toLowerCase()
            .split(",")
            .map((s) => s.trim());
          const jdSkills = Array.isArray(jobDescription.skills)
            ? jobDescription.skills.map((s) => s.toLowerCase())
            : jobDescription.skills
                .toLowerCase()
                .split(",")
                .map((s) => s.trim());

          const aMatches = jdSkills.filter((skill) =>
            aSkills.includes(skill)
          ).length;
          const bMatches = jdSkills.filter((skill) =>
            bSkills.includes(skill)
          ).length;

          return bMatches - aMatches;
        });
    };

    const getAllProfiles = async () => {
      try {
        setIsLoading(true);

        if (!dashboardJobDescription) {
          setJobData([]);
          setIsLoading(false);
          return;
        }

        const data = await fetch(
          `${
            import.meta.env.VITE_APP_HOST
          }/api/v1/getSuggestedCandidates?emp_id=${emp_id}`
        );
        let res = await data.json();

        if (isMounted && res.candidateProfiles?.length) {
          const matchingProfiles = filterMatchingProfiles(
            res.candidateProfiles,
            dashboardJobDescription
          );
          setJobData(matchingProfiles);
        } else {
          setJobData([]);
        }
        setIsLoading(false);
      } catch (err) {
        console.error(`Error fetching profiles:`, err);
        if (isMounted) {
          setIsLoading(false);
          setJobData([]);
        }
      }
    };

    if (dashboardJobDescription) {
      getAllProfiles();
    }

    return () => {
      isMounted = false;
    };
  }, [emp_id, dashboardJobDescription]);

  useEffect(() => {
    const fetchCandidates = async () => {
      try {
        const profileData = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/employerProfilesData`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: emp_id,
            },
          }
        );
        const profileJson = await profileData.json();

        if (profileJson.data) {
          setShortListedCandidatesCount(
            profileJson?.data?.candidate_counts?.shortlisted_count || 0
          );
          setUnlockedCandidatesCount(
            profileJson?.data?.candidate_counts?.unlocked_count || 0
          );
          setInterviewedCandidatesCount(
            profileJson?.data?.candidate_counts?.interviewed_count || 0
          );
          setOfferedCandidatesCount(
            profileJson?.data?.candidate_counts?.offered_count || 0
          );
          setEmpData({
            name: profileJson?.data?.emp_name || "Default User",
            plan_name: profileJson?.data?.plan_name || "PRO",
            creditsLeft: profileJson?.data?.creditsLeft,
            totalCredits: profileJson?.data?.totalCredits || profileJson?.data?.credits_assigned || 10, // 🎯 CREDIT FIX: Use dynamic total credits
            profile_picture: profileJson?.data?.profile_picture || "",
            company_logo: profileJson?.data?.company_logo || "",
            company_name: profileJson?.data?.company_name || "",
          });
        }
      } catch (err) {
        console.error("Error fetching shortlisted profiles:", err);
      }
    };

    // Fetch job description for dashboard
    const fetchDashboardJobDescription = async () => {
      setJdLoading(true);
      try {
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/job-description/${emp_id}`
        );
        if (response.ok) {
          const data = await response.json();
          setDashboardJobDescription(
            Array.isArray(data.jobDescriptions)
              ? data.jobDescriptions[0] || null
              : data.jobDescription || null
          );
        } else if (response.status === 404) {
          // No job description found, do not log error
          setDashboardJobDescription(null);
        } else {
          // Only log unexpected errors
          console.error(
            "Failed to fetch job description:",
            response.statusText
          );
          setDashboardJobDescription(null);
        }
      } catch (err) {
        console.error("Failed to fetch job description:", err);
        setDashboardJobDescription(null);
      }
      setJdLoading(false);
    };

    fetchCandidates();
    fetchDashboardJobDescription();
  }, [emp_id, isModalOpen]);

  // Listen for company logo updates
  useEffect(() => {
    const handleLogoUpdate = (event) => {
      // Refresh employer data when logo is updated
      const fetchCandidates = async () => {
        try {
          const profileData = await fetch(
            `${import.meta.env.VITE_APP_HOST}/api/v1/employerProfilesData`,
            {
              method: "GET",
              headers: {
                "Content-Type": "application/json",
                Authorization: emp_id,
              },
            }
          );
          const profileJson = await profileData.json();

          if (profileJson.data) {
            setEmpData({
              name: profileJson?.data?.emp_name || "Default User",
              plan_name: profileJson?.data?.plan_name || "PRO",
              creditsLeft: profileJson?.data?.creditsLeft,
              totalCredits: profileJson?.data?.totalCredits || profileJson?.data?.credits_assigned || 10, // 🎯 CREDIT FIX: Use dynamic total credits
              profile_picture: profileJson?.data?.profile_picture || "",
              company_logo: profileJson?.data?.company_logo || "",
              company_name: profileJson?.data?.company_name || "",
            });
          }
        } catch (err) {
          console.error("Error fetching updated employer data:", err);
        }
      };

      fetchCandidates();
    };

    window.addEventListener('companyLogoUpdated', handleLogoUpdate);

    return () => {
      window.removeEventListener('companyLogoUpdated', handleLogoUpdate);
    };
  }, [emp_id]);

  // Delete handler for dashboard JD
  const handleDashboardDeleteJD = async () => {
    if (!dashboardJobDescription?._id) return;
    setJdLoading(true);
    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/job-description/${
          dashboardJobDescription._id
        }`,
        { method: "DELETE" }
      );
      if (response.ok) {
        toast.success("Job description deleted");
        setDashboardJobDescription(null);
      } else {
        toast.error("Failed to delete job description");
      }
    } catch {
      toast.error("Failed to delete job description");
    }
    setJdLoading(false);
  };

  return (
    <>
      {jstoken ? (

        <div className="p-4 space-y-8">
          {/* Header Section - Matching Reference Design */}
          <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-xl shadow-sm">
            <div className="flex items-center justify-between p-6">
              {/* Left: Profile Section */}
              <div className="flex items-center gap-4 p-4 border border-gray-200 dark:border-gray-700 rounded-xl bg-white dark:bg-gray-800">
                <div className="relative">
                  <img
                    src={
                      empData?.company_logo
                        ? empData.company_logo.startsWith("http")
                            ? empData.company_logo
                            : `${import.meta.env.VITE_APP_HOST}/${empData.company_logo}`
                        : empData?.profile_picture
                        ? empData.profile_picture.startsWith("http")
                            ? empData.profile_picture
                            : `${import.meta.env.VITE_APP_HOST}/${empData.profile_picture}`
                        : avatar
                    }
                    alt={empData?.company_logo ? "Company Logo" : "User Avatar"}
                    className="h-16 w-16 rounded-full shadow-md object-cover"
                    onError={(e) => {
                      if (e.target.src !== avatar) {
                        e.target.onerror = null;
                        e.target.src = avatar;
                      }
                    }}
                  />

                </div>
                <div className="space-y-2">
                  <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                    {empData?.name || "Loading..."}
                  </h1>
                  <div className="inline-flex items-center px-2.5 py-0.5 rounded text-xs font-medium bg-blue-50 text-blue-700 dark:bg-blue-900/50 border border-blue-300">
                    {empData?.plan_name || "Free Employer Plan"}
                  </div>

                  {/* 🎯 EMPLOYER MEMBERSHIP: Enhanced credit display with usage bar */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-500 dark:text-gray-400">Credits Used</span>
                      <span className="font-semibold text-gray-900 dark:text-white">
                        {Math.max(0, (empData?.totalCredits || 10) - (empData?.creditsLeft || 0))} / {empData?.totalCredits || 10}
                      </span>
                    </div>

                    {/* Credit usage progress bar */}
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-300"
                        style={{
                          width: `${Math.min(100, Math.max(0, (((empData?.totalCredits || 10) - (empData?.creditsLeft || 0)) / (empData?.totalCredits || 10)) * 100))}%`
                        }}
                      ></div>
                    </div>

                    <div className="flex items-center justify-between">
                      
                      {/* Show upgrade button when credits are low */}
                      {(empData?.creditsLeft || 0) <= 2 && (
                        <button
                          onClick={() => {
                            const formattedStatus = getFormattedEmployerMembershipStatus({
                              currentCredits: empData?.creditsLeft || 0,
                              planName: empData?.plan_name || "Free Employer Plan",
                              canUseCredits: (empData?.creditsLeft || 0) > 0,
                              needsUpgrade: (empData?.creditsLeft || 0) <= 2
                            });
                            setMembershipStatus(formattedStatus);
                            setShowMembershipModal(true);
                          }}
                          className="inline-flex items-center gap-1 rounded-full bg-gradient-to-r from-green-600 to-green-700 px-2.5 py-1 text-xs font-medium text-white hover:from-green-700 hover:to-green-800 transition-all duration-200"
                        >
                        <ArrowUpCircle className="w-3 h-3" />
                        Upgrade Plan
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Right: Colorful Stats Cards */}
              <div className="hidden lg:flex items-center gap-3">
                <div
                  className="bg-blue-500 hover:bg-blue-600 text-white rounded-xl px-4 py-3 min-w-[100px] text-center cursor-pointer transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                  onClick={() => navigate("/employer/track-candidates?tab=Shortlisted")}
                >
                  <div className="text-xs font-medium opacity-90 mb-1">Shortlisted</div>
                  <div className="text-2xl font-bold">{shortListedCandidatesCount}</div>
                </div>
                <div
                  className="bg-orange-500 hover:bg-orange-600 text-white rounded-xl px-4 py-3 min-w-[100px] text-center cursor-pointer transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                  onClick={() => navigate("/employer/track-candidates?tab=Unlocked")}
                >
                  <div className="text-xs font-medium opacity-90 mb-1">Unlocked</div>
                  <div className="text-2xl font-bold">{unlockedCandidatesCount}</div>
                </div>
                <div
                  className="bg-red-500 hover:bg-red-600 text-white rounded-xl px-4 py-3 min-w-[100px] text-center cursor-pointer transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                  onClick={() => navigate("/employer/track-candidates?tab=Interviews")}
                >
                  <div className="text-xs font-medium opacity-90 mb-1">Interviews</div>
                  <div className="text-2xl font-bold">{InterviewedCandidatesCount}</div>
                </div>
                <div
                  className="bg-green-500 hover:bg-green-600 text-white rounded-xl px-4 py-3 min-w-[100px] text-center cursor-pointer transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                  onClick={() => navigate("/employer/track-candidates?tab=Offers")}
                >
                  <div className="text-xs font-medium opacity-90 mb-1">Offers</div>
                  <div className="text-2xl font-bold">{offeredCandidatesCount}</div>

                </div>
              </div>
            </div>

            {/* Mobile Stats */}
            <div className="lg:hidden border-t border-gray-200 dark:border-gray-800 p-4">
              <div className="grid grid-cols-2 gap-3">
                <div
                  className="bg-blue-500 text-white rounded-lg p-3 text-center cursor-pointer"
                  onClick={() => navigate("/employer/track-candidates?tab=Shortlisted")}
                >
                  <div className="text-xs font-medium opacity-90 mb-1">Shortlisted</div>
                  <div className="text-xl font-bold">{shortListedCandidatesCount}</div>
                </div>
                <div
                  className="bg-orange-500 text-white rounded-lg p-3 text-center cursor-pointer"
                  onClick={() => navigate("/employer/track-candidates?tab=Unlocked")}
                >
                  <div className="text-xs font-medium opacity-90 mb-1">Unlocked</div>
                  <div className="text-xl font-bold">{unlockedCandidatesCount}</div>
                </div>
                <div
                  className="bg-red-500 text-white rounded-lg p-3 text-center cursor-pointer"
                  onClick={() => navigate("/employer/track-candidates?tab=Interviews")}
                >
                  <div className="text-xs font-medium opacity-90 mb-1">Interviews</div>
                  <div className="text-xl font-bold">{InterviewedCandidatesCount}</div>
                </div>
                <div
                  className="bg-green-500 text-white rounded-lg p-3 text-center cursor-pointer"
                  onClick={() => navigate("/employer/track-candidates?tab=Offers")}
                >
                  <div className="text-xs font-medium opacity-90 mb-1">Offers</div>
                  <div className="text-xl font-bold">{offeredCandidatesCount}</div>

                </div>
              </div>
            </div>
          </div>

          {/* AI Candidate Matching Section - Full Width */}
          <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-xl shadow-sm">
            <div className="p-6 border-b border-gray-200 dark:border-gray-800">
              <div className="flex items-center gap-3">
                <Bot className="w-8 h-8 text-blue-600" />
                <div>
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                    AI Candidate Matching
                  </h2>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Click a job description below to view matching candidates.
                  </p>
                </div>
              </div>
            </div>
            <div className="p-6">
              <JobDescriptionList emp_id={emp_id} ref={jobDescListRef} onUploadClick={() => setModalOpen(true)} />
             {/* Removed Upload Job Description button from here */}
            </div>
          </div>

          <JobDescriptionModal
            isOpen={isModalOpen}
            onClose={() => setModalOpen(false)}
            onJobDescriptionChange={handleJobDescriptionChange}
          />
        </div>
      ) : (
        <div className="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Please sign in to continue
            </h2>
            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
              You need to be authenticated to access the dashboard
            </p>
          </div>
        </div>
      )}

      {/* 🎯 EMPLOYER MEMBERSHIP: Universal membership modal for credit limits */}
      <UniversalMembershipModal
        isOpen={showMembershipModal}
        onClose={() => setShowMembershipModal(false)}
        userType="employer"
        membershipStatus={membershipStatus}
        whatsappNumber="[WHATSAPP_NUMBER_PLACEHOLDER]"
      />
    </>
  );
};

export default EmployerDashboard;