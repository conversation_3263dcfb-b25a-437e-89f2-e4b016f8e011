const prisma = require("../config/prisma");

// 🎯 EMPLOYER MEMBERSHIP VALIDATION: Helper function to check employer's credit status
const checkEmployerMembershipStatus = async (empId) => {
  try {
    // Get employer plan details
    const employerPlan = await prisma.employerplans.findFirst({
      where: { emp_id: Number(empId) },
      include: {
        plans: true
      }
    });

    if (!employerPlan) {
      return {
        canUseCredits: false,
        currentCredits: 0,
        planName: 'No Plan',
        statusText: 'No active plan found',
        statusColor: 'error',
        needsUpgrade: true
      };
    }

    const currentCredits = employerPlan.creditsLeft || 0;
    const planName = employerPlan.plans.plan_name || 'Unknown Plan';
    const canUseCredits = currentCredits > 0;

    return {
      canUseCredits,
      currentCredits,
      planName,
      statusText: canUseCredits
        ? `${currentCredits} credit${currentCredits !== 1 ? 's' : ''} remaining`
        : 'No credits remaining - upgrade needed',
      statusColor: canUseCredits ? 'success' : 'error',
      needsUpgrade: !canUseCredits
    };
  } catch (error) {
    console.error('Error checking employer membership status:', error);
    return {
      canUseCredits: false,
      currentCredits: 0,
      planName: 'Error',
      statusText: 'Error checking membership status',
      statusColor: 'error',
      needsUpgrade: true
    };
  }
};

const employerController = {
  // Check employer email uniqueness endpoint
  checkEmployerEmailUniqueness: async (req, res) => {
    try {
      const { email } = req.body;

      if (!email) {
        return res.status(400).json({ message: 'Email is required' });
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return res.status(400).json({ message: 'Invalid email format' });
      }

      const existingUser = await prisma.user.findUnique({
        where: { email },
      });

      if (existingUser) {
        return res.status(409).json({
          message: 'Email already exists. Please log in.',
          field: 'email',
          available: false
        });
      }

      return res.status(200).json({
        message: 'Email is available',
        available: true
      });
    } catch (err) {
      console.error('Error checking employer email uniqueness:', err);
      return res.status(500).json({ error: 'Internal server error' });
    }
  },

  // 🎯 EMPLOYER MEMBERSHIP STATUS: Get employer's membership and credit information
  getEmployerMembershipStatus: async (req, res) => {
    try {
      const { emp_id } = req.params;

      if (!emp_id) {
        return res.status(400).json({ message: 'Employer ID is required' });
      }

      const membershipStatus = await checkEmployerMembershipStatus(emp_id);

      return res.status(200).json({
        message: 'Employer membership status retrieved successfully',
        membershipStatus
      });
    } catch (err) {
      console.error('Error getting employer membership status:', err);
      return res.status(500).json({ error: 'Internal server error' });
    }
  },

  // 🎯 ADMIN API: Get all employers with membership information for admin dashboard
  getAdminEmployers: async (req, res) => {
    try {
      // Get all employers with their basic information, user details, company info, and membership plans
      const employers = await prisma.employer.findMany({
        select: {
          id: true,
          emp_id: true,
          emp_name: true,
          emp_email: true,
          emp_mobile: true,
          designation: true,
          profile_picture: true,
          created_at: true,
          company_id: true,
          user: {
            select: {
              id: true,
              email: true,
              created_at: true,
              updated_at: true
            }
          },
          company: {
            select: {
              id: true,
              company_name: true,
              company_description: true,
              company_website: true,
              company_logo: true
            }
          },
          employerplans: {
            select: {
              id: true,
              plan_id: true,
              start_date: true,
              end_date: true,
              creditsLeft: true,
              plans: {
                select: {
                  id: true,
                  plan_name: true,
                  plan_description: true,
                  plan_price: true,
                  plan_duration_days: true,
                  credits_assigned: true
                }
              }
            },
            orderBy: {
              start_date: 'desc'
            },
            take: 1 // Get the most recent plan
          }
        },
        orderBy: {
          created_at: 'desc'
        }
      });

      // Transform the data to match the expected format for the frontend
      const transformedEmployers = employers.map(employer => {
        const currentPlan = employer.employerplans[0];
        const membershipStatus = currentPlan ?
          (currentPlan.creditsLeft > 5 ? 'active' :
           currentPlan.creditsLeft > 0 ? 'warning' : 'expired') : 'expired';

        // Count profiles unlocked by this employer
        const profilesUnlockedPromise = prisma.employerprofiles.count({
          where: {
            emp_id: employer.id,
            status: 'unlocked'
          }
        });

        return {
          id: employer.id,
          emp_id: employer.emp_id,
          name: employer.emp_name,
          email: employer.emp_email,
          mobile: employer.emp_mobile?.toString(),
          designation: employer.designation,
          company: employer.company?.company_name || 'No Company',
          company_id: employer.company_id,
          company_logo: employer.company?.company_logo,
          profile_picture: employer.profile_picture,
          creditsLeft: currentPlan?.creditsLeft || 0,
          totalCredits: currentPlan?.plans?.credits_assigned || 10,
          planName: currentPlan?.plans?.plan_name || 'Free Employer Plan',
          membershipStatus,
          joinDate: employer.created_at,
          lastActive: employer.user?.updated_at || employer.created_at,
          profilesUnlockedPromise, // We'll resolve this separately
          location: null // This field doesn't exist in the current schema
        };
      });

      // Resolve profiles unlocked count for all employers
      const employersWithProfileCounts = await Promise.all(
        transformedEmployers.map(async (employer) => {
          const profilesUnlocked = await employer.profilesUnlockedPromise;
          delete employer.profilesUnlockedPromise;
          return {
            ...employer,
            profilesUnlocked
          };
        })
      );

      return res.status(200).json({
        message: 'Employers retrieved successfully',
        data: employersWithProfileCounts,
        total: employersWithProfileCounts.length
      });
    } catch (err) {
      console.error('Error getting admin employers:', err);
      return res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to fetch employers'
      });
    }
  },

  // 🎯 ADMIN API: Update employer credit limit with usage reset
  updateEmployerCredits: async (req, res) => {
    try {
      const { emp_id } = req.params;
      const { credits } = req.body;

      if (!emp_id) {
        return res.status(400).json({ message: 'Employer ID is required' });
      }

      if (credits === undefined || credits === null) {
        return res.status(400).json({ message: 'Credit limit is required' });
      }

      const newCreditLimit = parseInt(credits);
      if (isNaN(newCreditLimit) || newCreditLimit < 0) {
        return res.status(400).json({ message: 'Credit limit must be a non-negative number' });
      }

      // 🎯 CREDIT VALIDATION: Enforce minimum credit limit of 10
      if (newCreditLimit < 10) {
        return res.status(400).json({
          message: 'Credit limit must be at least 10 credits',
          minLimit: 10,
          providedLimit: newCreditLimit
        });
      }

      // Find the employer
      const employer = await prisma.employer.findUnique({
        where: { id: parseInt(emp_id) },
        include: {
          employerplans: {
            include: {
              plans: true
            },
            orderBy: {
              start_date: 'desc'
            },
            take: 1
          }
        }
      });

      if (!employer) {
        return res.status(404).json({ message: 'Employer not found' });
      }

      let currentPlan = employer.employerplans[0];

      // 🎯 SIMPLIFIED CREDIT LOGIC: Reset usage history - give full credit amount
      const newCreditsLeft = newCreditLimit;

      console.log(`🎯 CREDIT RESET DEBUG:
        - New Credit Limit: ${newCreditLimit}
        - Credits Left (Reset): ${newCreditsLeft}
        - Usage History: RESET TO ZERO
      `);

      // Determine if we need to change the plan based on new credit limit
      let newPlanName = 'Free Employer Plan';
      if (newCreditLimit > 10) {
        newPlanName = 'Custom Plan';
      }

      // For Custom Plan, always update the plan's credits_assigned to match the new credit limit
      if (newPlanName === 'Custom Plan') {
        // Find or create the Custom Plan
        let targetPlan = await prisma.plans.findFirst({
          where: {
            plan_name: 'Custom Plan',
            role: 'emp'
          }
        });

        if (!targetPlan) {
          // Create the Custom Plan if it doesn't exist
          targetPlan = await prisma.plans.create({
            data: {
              plan_name: 'Custom Plan',
              plan_description: 'Custom plan with admin-assigned credits',
              plan_price: 0,
              plan_duration_days: 365,
              role: 'emp',
              credits_assigned: newCreditLimit,
              features: 'Custom credit allocation, Premium support'
            }
          });
        } else {
          // Always update the Custom Plan's credits to match the new assignment
          targetPlan = await prisma.plans.update({
            where: { id: targetPlan.id },
            data: {
              credits_assigned: newCreditLimit
            }
          });
        }

        // Update or create employer plan
        if (currentPlan) {
          await prisma.employerplans.update({
            where: { id: currentPlan.id },
            data: {
              plan_id: targetPlan.id,
              creditsLeft: newCreditsLeft
            }
          });
        } else {
          await prisma.employerplans.create({
            data: {
              emp_id: employer.id,
              plan_id: targetPlan.id,
              creditsLeft: newCreditsLeft,
              start_date: new Date(),
              end_date: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year from now
            }
          });
        }
      } else {
        // For Free Plan, find or create the Free Plan
        let targetPlan = await prisma.plans.findFirst({
          where: {
            plan_name: 'Free Employer Plan',
            role: 'emp'
          }
        });

        if (!targetPlan) {
          // Create the Free Plan if it doesn't exist
          targetPlan = await prisma.plans.create({
            data: {
              plan_name: 'Free Employer Plan',
              plan_description: 'Default free plan for employers',
              plan_price: 0,
              plan_duration_days: 365,
              role: 'emp',
              credits_assigned: 10,
              features: 'Basic features, Standard support'
            }
          });
        }

        // Update or create employer plan
        if (currentPlan) {
          await prisma.employerplans.update({
            where: { id: currentPlan.id },
            data: {
              plan_id: targetPlan.id,
              creditsLeft: newCreditsLeft
            }
          });
        } else {
          await prisma.employerplans.create({
            data: {
              emp_id: employer.id,
              plan_id: targetPlan.id,
              creditsLeft: newCreditsLeft,
              start_date: new Date(),
              end_date: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year from now
            }
          });
        }
      }



      return res.status(200).json({
        message: 'Employer credit limit updated and usage history reset',
        data: {
          emp_id: employer.id,
          creditLimit: newCreditLimit,
          creditsLeft: newCreditsLeft,
          creditsUsed: 0, // Always 0 after reset
          planName: newPlanName,
          usageReset: true
        }
      });
    } catch (err) {
      console.error('Error updating employer credits:', err);
      return res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to update employer credits'
      });
    }
  },
};

module.exports = employerController;
