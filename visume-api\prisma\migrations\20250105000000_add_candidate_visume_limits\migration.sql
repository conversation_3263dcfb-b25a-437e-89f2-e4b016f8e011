-- Add visume limit management fields to jobseekerplans table
-- Following the same pattern as employer credit management

-- Add visume_limit field (equivalent to credits_assigned for employers)
ALTER TABLE `jobseekerplans` ADD COLUMN `visume_limit` INTEGER NULL DEFAULT 1;

-- Add visumes_used field to track current usage
ALTER TABLE `jobseekerplans` ADD COLUMN `visumes_used` INTEGER NULL DEFAULT 0;

-- Update existing records to have default values
UPDATE `jobseekerplans` SET `visume_limit` = 1 WHERE `visume_limit` IS NULL;
UPDATE `jobseekerplans` SET `visumes_used` = 0 WHERE `visumes_used` IS NULL;

-- Add indexes for performance
CREATE INDEX `idx_jobseekerplans_visume_limit` ON `jobseekerplans`(`visume_limit`);
CREATE INDEX `idx_jobseekerplans_visumes_used` ON `jobseekerplans`(`visumes_used`);
