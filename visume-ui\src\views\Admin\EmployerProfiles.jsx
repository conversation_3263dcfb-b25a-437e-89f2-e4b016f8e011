import React, { useState, useEffect } from 'react';
import {
  Users,
  Building2,
  CreditCard,
  Crown,
  Mail,
  MapPin,
  Edit3,
  Search,
  Filter,
  Calendar,
  Activity,
  Phone,
  Briefcase
} from "lucide-react";
import { toast } from "react-hot-toast";
import avatar from "assets/img/avatars/avatar4.png";

const EmployerProfiles = () => {
  const [employers, setEmployers] = useState([]);
  const [filteredEmployers, setFilteredEmployers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [selectedEmployer, setSelectedEmployer] = useState(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editCredits, setEditCredits] = useState("");
  const [isUpdatingCredits, setIsUpdatingCredits] = useState(false);

  // Fetch employers from API
  useEffect(() => {
    const fetchEmployers = async () => {
      setLoading(true);
      try {
        const response = await fetch(`${import.meta.env.VITE_APP_HOST}/api/v1/admin/employers`);
        const result = await response.json();

        if (response.ok) {
          setEmployers(result.data || []);
          setFilteredEmployers(result.data || []);
        } else {
          console.error("Error fetching employers:", result.message);
          toast.error("Failed to load employers");
          // Fallback to empty array
          setEmployers([]);
          setFilteredEmployers([]);
        }
      } catch (error) {
        console.error("Error fetching employers:", error);
        toast.error("Failed to load employers");
        // Fallback to empty array
        setEmployers([]);
        setFilteredEmployers([]);
      } finally {
        setLoading(false);
      }
    };

    fetchEmployers();
  }, []);

  // Filter employers based on search term and status
  useEffect(() => {
    let filtered = employers;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(employer =>
        employer.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        employer.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        employer.company?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        employer.designation?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter(employer => employer.membershipStatus === statusFilter);
    }

    setFilteredEmployers(filtered);
  }, [employers, searchTerm, statusFilter]);

  const handleEditCredits = (employer) => {
    setSelectedEmployer(employer);
    // Ensure initial value meets minimum requirement of 10 credits
    const currentLimit = employer.totalCredits || 10;
    setEditCredits(Math.max(currentLimit, 10).toString());
    setShowEditModal(true);
  };

  const handleSaveCredits = async () => {
    if (!selectedEmployer) return;

    try {
      const newCredits = parseInt(editCredits);
      if (isNaN(newCredits) || newCredits < 0) {
        toast.error("Please enter a valid credit limit");
        return;
      }

      // 🎯 CREDIT VALIDATION: Enforce minimum credit limit of 10
      if (newCredits < 10) {
        toast.error("Credit limit must be at least 10 credits");
        return;
      }

      setIsUpdatingCredits(true);

      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/admin/employer/${selectedEmployer.id}/credits`,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ credits: newCredits }),
        }
      );

      const result = await response.json();

      if (response.ok) {
        // Update local state with new credit limit and remaining credits
        const updatedEmployers = employers.map(emp =>
          emp.id === selectedEmployer.id
            ? {
                ...emp,
                creditsLeft: result.data.creditsLeft,
                totalCredits: result.data.creditLimit,
                planName: result.data.planName,
                membershipStatus: result.data.creditsLeft > 5 ? 'active' : result.data.creditsLeft > 0 ? 'warning' : 'expired'
              }
            : emp
        );

        setEmployers(updatedEmployers);
        setFilteredEmployers(updatedEmployers.filter(emp => {
          // Apply current filters
          let matches = true;
          if (searchTerm) {
            matches = matches && (
              emp.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
              emp.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
              emp.company?.toLowerCase().includes(searchTerm.toLowerCase()) ||
              emp.designation?.toLowerCase().includes(searchTerm.toLowerCase())
            );
          }
          if (statusFilter !== "all") {
            matches = matches && emp.membershipStatus === statusFilter;
          }
          return matches;
        }));

        setShowEditModal(false);
        setSelectedEmployer(null);
        setEditCredits("");

        toast.success(`Credit limit updated and usage history reset for ${selectedEmployer.name}`);
      } else {
        console.error("Error updating credits:", result.message);
        toast.error(result.message || "Failed to update credit limit");
      }
    } catch (error) {
      console.error("Error updating credits:", error);
      toast.error("Failed to update credit limit");
    } finally {
      setIsUpdatingCredits(false);
    }
  };

  const handleCancelEdit = () => {
    setShowEditModal(false);
    setSelectedEmployer(null);
    setEditCredits("");
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatMobile = (mobile) => {
    if (!mobile) return 'N/A';
    const mobileStr = mobile.toString();
    if (mobileStr.length === 10) {
      return `+91 ${mobileStr.slice(0, 5)} ${mobileStr.slice(5)}`;
    }
    return mobileStr;
  };

  // Status Badge Component
  const StatusBadge = ({ status, type = "membership" }) => {
    const getStatusConfig = () => {
      if (type === "membership") {
        switch (status) {
          case 'active':
            return { bg: 'bg-green-100 dark:bg-green-900/30', text: 'text-green-800 dark:text-green-300', label: 'Active' };
          case 'warning':
            return { bg: 'bg-yellow-100 dark:bg-yellow-900/30', text: 'text-yellow-800 dark:text-yellow-300', label: 'Low Credits' };
          case 'expired':
            return { bg: 'bg-red-100 dark:bg-red-900/30', text: 'text-red-800 dark:text-red-300', label: 'Expired' };
          default:
            return { bg: 'bg-gray-100 dark:bg-gray-700', text: 'text-gray-800 dark:text-gray-300', label: 'Unknown' };
        }
      }
      return { bg: 'bg-gray-100 dark:bg-gray-700', text: 'text-gray-800 dark:text-gray-300', label: status };
    };

    const config = getStatusConfig();
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        {config.label}
      </span>
    );
  };

  // Action Button Component
  const ActionButton = ({ onClick, variant = "primary", size = "sm", icon: Icon, children, disabled = false }) => {
    const baseClasses = "inline-flex items-center gap-1.5 font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2";
    const variants = {
      primary: "bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500",
      secondary: "bg-gray-100 hover:bg-gray-200 text-gray-700 focus:ring-gray-500 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-300",
      danger: "bg-red-600 hover:bg-red-700 text-white focus:ring-red-500"
    };
    const sizes = {
      xs: "px-2 py-1 text-xs",
      sm: "px-3 py-1.5 text-sm",
      md: "px-4 py-2 text-sm"
    };

    return (
      <button
        onClick={onClick}
        disabled={disabled}
        className={`${baseClasses} ${variants[variant]} ${sizes[size]} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      >
        {Icon && <Icon className="w-4 h-4" />}
        {children}
      </button>
    );
  };

  // Employer Card Component
  const EmployerCard = ({ employer }) => {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-200">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center gap-4">
            <div className="relative">
              <img
                src={
                  employer.profile_picture
                    ? employer.profile_picture.startsWith("http")
                      ? employer.profile_picture
                      : `${import.meta.env.VITE_APP_HOST}/${employer.profile_picture}`
                    : avatar
                }
                alt={employer.name}
                className="w-12 h-12 rounded-full object-cover border-2 border-gray-200 dark:border-gray-600"
                onError={(e) => {
                  if (e.target.src !== avatar) {
                    e.target.onerror = null;
                    e.target.src = avatar;
                  }
                }}
              />
              <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white dark:border-gray-800 ${
                employer.membershipStatus === 'active' ? 'bg-green-500' :
                employer.membershipStatus === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
              }`}></div>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {employer.name}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {employer.designation} {employer.company && `at ${employer.company}`}
              </p>
              <div className="flex items-center gap-4 mt-1 text-xs text-gray-500 dark:text-gray-400">
                <span className="flex items-center gap-1">
                  <Mail className="w-3 h-3" />
                  {employer.email}
                </span>
                {employer.mobile && (
                  <span className="flex items-center gap-1">
                    <Phone className="w-3 h-3" />
                    {formatMobile(employer.mobile)}
                  </span>
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <StatusBadge status={employer.membershipStatus} type="membership" />
            <ActionButton
              onClick={() => handleEditCredits(employer)}
              variant="primary"
              size="xs"
              icon={CreditCard}
            >
              Manage Credits
            </ActionButton>
          </div>
        </div>

        {/* Membership and Activity Info */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
          <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {employer.creditsLeft}
            </div>
            <div className="text-xs text-blue-600 dark:text-blue-400 font-medium">
              Credits Left
            </div>
          </div>
          <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">
              {employer.profilesUnlocked || 0}
            </div>
            <div className="text-xs text-green-600 dark:text-green-400 font-medium">
              Profiles Unlocked
            </div>
          </div>
          <div className="text-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
            <div className="text-sm font-bold text-purple-600 dark:text-purple-400">
              {employer.planName}
            </div>
            <div className="text-xs text-purple-600 dark:text-purple-400 font-medium">
              Current Plan
            </div>
          </div>
          <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="text-sm font-bold text-gray-600 dark:text-gray-400">
              {formatDate(employer.lastActive)}
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400 font-medium">
              Last Active
            </div>
          </div>
        </div>

        {/* Credit Usage Progress */}
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Credit Remaining</span>
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {employer.creditsLeft} / {employer.totalCredits}
            </span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${
                employer.membershipStatus === 'active' ? 'bg-green-500' :
                employer.membershipStatus === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
              }`}
              style={{
                width: `${Math.max(0, Math.min(100, ((employer.totalCredits - employer.creditsLeft) / employer.totalCredits) * 100))}%`
              }}
            ></div>
          </div>
        </div>

        {/* Join Date */}
        <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
          <span className="flex items-center gap-1">
            <Calendar className="w-4 h-4" />
            Joined {formatDate(employer.joinDate)}
          </span>
          {employer.company && (
            <span className="flex items-center gap-1">
              <Building2 className="w-4 h-4" />
              {employer.company}
            </span>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Header */}
        <div className="mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 p-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-3 font-sora">
                  Employer Profiles
                </h1>
                <p className="text-lg text-gray-600 dark:text-gray-300 font-manrope">
                  Manage and monitor employer accounts and their membership status
                </p>
              </div>
              <div className="hidden md:flex items-center space-x-4">
                <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-3 rounded-xl">
                  <Building2 className="w-8 h-8 text-white" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Employers</p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">{employers.length}</p>
              </div>
              <div className="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-lg">
                <Users className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active Employers</p>
                <p className="text-3xl font-bold text-green-600 dark:text-green-400">
                  {employers.filter(emp => emp.membershipStatus === 'active').length}
                </p>
              </div>
              <div className="bg-green-100 dark:bg-green-900/30 p-3 rounded-lg">
                <Activity className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Low Credits</p>
                <p className="text-3xl font-bold text-yellow-600 dark:text-yellow-400">
                  {employers.filter(emp => emp.membershipStatus === 'warning').length}
                </p>
              </div>
              <div className="bg-yellow-100 dark:bg-yellow-900/30 p-3 rounded-lg">
                <CreditCard className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Expired</p>
                <p className="text-3xl font-bold text-red-600 dark:text-red-400">
                  {employers.filter(emp => emp.membershipStatus === 'expired').length}
                </p>
              </div>
              <div className="bg-red-100 dark:bg-red-900/30 p-3 rounded-lg">
                <Crown className="w-6 h-6 text-red-600 dark:text-red-400" />
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 p-6 mb-8">
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-4 items-center w-full md:w-auto">
              {/* Search */}
              <div className="relative w-full md:w-80">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search employers..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>

              {/* Status Filter */}
              <div className="relative">
                <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="pl-10 pr-8 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white appearance-none"
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="warning">Low Credits</option>
                  <option value="expired">Expired</option>
                </select>
              </div>
            </div>

            <div className="text-sm text-gray-600 dark:text-gray-400">
              Showing {filteredEmployers.length} of {employers.length} employers
            </div>
          </div>
        </div>

        {/* Employers Grid */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 p-6">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            </div>
          ) : filteredEmployers.length > 0 ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {filteredEmployers.map((employer) => (
                <EmployerCard key={employer.id} employer={employer} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Building2 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No employers found
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                {searchTerm || statusFilter !== "all"
                  ? "Try adjusting your search criteria or filters"
                  : "No employers have registered yet"
                }
              </p>
            </div>
          )}
        </div>

        {/* Credit Management Modal */}
        {showEditModal && selectedEmployer && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 w-full max-w-md mx-4 shadow-2xl">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                Set Credit Limit for {selectedEmployer.name}
              </h3>

              {/* Usage Reset Warning */}
              <div className="mb-6 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0">
                    <svg className="w-5 h-5 text-amber-600 dark:text-amber-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-amber-800 dark:text-amber-200 mb-1">
                      Usage History Will Be Reset
                    </h4>
                    <p className="text-sm text-amber-700 dark:text-amber-300">
                      Setting a new credit limit will reset the employer's usage history to zero and provide the full credit amount.
                    </p>
                  </div>
                </div>
              </div>

              {/* Current Status */}
              <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Current Credit Limit:</span>
                  <span className="text-lg font-bold text-gray-900 dark:text-white">{selectedEmployer.totalCredits}</span>
                </div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Credits Remaining:</span>
                  <span className="text-lg font-bold text-gray-900 dark:text-white">{selectedEmployer.creditsLeft}</span>
                </div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Credits Used:</span>
                  <span className="text-lg font-bold text-gray-900 dark:text-white">{(selectedEmployer.totalCredits || 10) - (selectedEmployer.creditsLeft || 0)}</span>
                </div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Current Plan:</span>
                  <span className="text-sm font-semibold text-purple-600 dark:text-purple-400">{selectedEmployer.planName}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Status:</span>
                  <StatusBadge status={selectedEmployer.membershipStatus} type="membership" />
                </div>
              </div>

              {/* 🎯 PREDEFINED CREDIT OPTIONS */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                  Quick Credit Options
                </label>
                <div className="grid grid-cols-3 gap-3 mb-4">
                  <button
                    type="button"
                    onClick={() => setEditCredits('15')}
                    className="px-4 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-lg font-medium transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg"
                    disabled={isUpdatingCredits}
                  >
                    15 Credits
                  </button>
                  <button
                    type="button"
                    onClick={() => setEditCredits('20')}
                    className="px-4 py-3 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-lg font-medium transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg"
                    disabled={isUpdatingCredits}
                  >
                    20 Credits
                  </button>
                  <button
                    type="button"
                    onClick={() => setEditCredits('25')}
                    className="px-4 py-3 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white rounded-lg font-medium transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg"
                    disabled={isUpdatingCredits}
                  >
                    25 Credits
                  </button>
                </div>
              </div>

              {/* Custom Credit Input */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Custom Credit Limit
                </label>
                <input
                  type="number"
                  value={editCredits}
                  onChange={(e) => setEditCredits(e.target.value)}
                  min="10"
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  placeholder="Enter credit limit (minimum 10)"
                  disabled={isUpdatingCredits}
                />
                <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                  • Minimum credit limit: 10 credits (usage history will be reset)
                  <br />
                  • Limit = 10: Free Employer Plan | Limit &gt; 10: Custom Plan
                </p>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3">
                <ActionButton
                  onClick={handleSaveCredits}
                  variant="primary"
                  size="md"
                  disabled={isUpdatingCredits}
                  icon={isUpdatingCredits ? null : CreditCard}
                >
                  {isUpdatingCredits ? (
                    <div className="flex items-center gap-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      Updating...
                    </div>
                  ) : (
                    'Update Credit Limit'
                  )}
                </ActionButton>
                <ActionButton
                  onClick={handleCancelEdit}
                  variant="secondary"
                  size="md"
                  disabled={isUpdatingCredits}
                >
                  Cancel
                </ActionButton>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EmployerProfiles;
